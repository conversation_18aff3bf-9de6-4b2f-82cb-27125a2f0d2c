#!/bin/bash

# UltraFast Index Performance Comparison Demo Script
# This script demonstrates the performance differences between all three implementations

set -e  # Exit on any error

echo "🚀 UltraFast Index Performance Comparison Demo"
echo "==============================================="
echo ""

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.19 or later."
    exit 1
fi

# Check if mock_data.csv exists
if [ ! -f "mock_data.csv" ]; then
    echo "❌ mock_data.csv not found. Please ensure the data file exists."
    exit 1
fi

# Check if existing_standalone directory exists
if [ ! -d "existing_standalone" ]; then
    echo "❌ existing_standalone directory not found."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Clean up previous demo results
echo "🧹 Cleaning up previous demo results..."
rm -rf demo_results
mkdir -p demo_results

# Initialize existing_standalone module if needed
echo "📦 Initializing existing_standalone module..."
cd existing_standalone
if [ ! -f "go.mod" ]; then
    go mod init existing_indexing
fi
go mod tidy
cd ..

# Initialize main module if needed
echo "📦 Initializing main module..."
if [ ! -f "go.mod" ]; then
    go mod init ultrafast_standalone
fi
go mod tidy

echo "✅ Setup completed"
echo ""

# Run the performance comparison
echo "🏃 Running performance comparison demo..."
echo "This may take several minutes depending on your system..."
echo ""

# Method 1: Run the demo function directly
echo "Starting comprehensive performance comparison..."
go run demo_performance_comparison.go

echo ""
echo "🎉 Demo completed successfully!"
echo ""
echo "📊 Results Summary:"
echo "==================="
echo ""
echo "The demo has tested all three implementations:"
echo "1. 🔵 Existing - Traditional binary format indexing"
echo "2. 🟡 ExistingInx - Compressed binary format indexing"  
echo "3. 🟢 UltraFast V1 - Memory-mapped hash table indexing"
echo "4. 🚀 UltraFast V2 - Advanced optimized indexing with compression"
echo ""
echo "📁 Generated files:"
echo "  - demo_results/existing/ - Traditional indexes"
echo "  - demo_results/existing_inx/ - Compressed indexes"
echo "  - demo_results/ultrafast_v1/ - UltraFast V1 indexes"
echo "  - demo_results/ultrafast_v2/ - UltraFast V2 indexes"
echo "  - demo_results/performance_report.txt - Detailed performance report"
echo ""
echo "💡 Key Metrics Compared:"
echo "  ⏱️  Index Generation Time"
echo "  💾 Storage Size"
echo "  🚀 Query Performance (average time & QPS)"
echo "  📈 Performance Improvements vs baseline"
echo ""
echo "🔍 For detailed analysis, check the performance report:"
echo "   cat demo_results/performance_report.txt"
echo ""
echo "✨ Demo completed! You can now analyze the performance differences."
