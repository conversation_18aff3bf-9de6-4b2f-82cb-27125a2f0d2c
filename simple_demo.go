package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// SimpleDemoConfig holds basic demo configuration
type SimpleDemoConfig struct {
	DataFile    string
	TestColumn  string
	TestValue   string
	OutputDir   string
}

func main() {
	fmt.Println("🚀 UltraFast Index Simple Performance Demo")
	fmt.Println("==========================================")
	fmt.Println()

	config := SimpleDemoConfig{
		DataFile:   "mock_data.csv",
		TestColumn: "protocol",
		TestValue:  "TCP",
		OutputDir:  "simple_demo_results",
	}

	// Verify prerequisites
	if err := verifySimplePrerequisites(config); err != nil {
		fmt.Printf("❌ Prerequisites check failed: %v\n", err)
		fmt.Println("\nTo fix this:")
		fmt.Println("1. Ensure Go 1.19+ is installed")
		fmt.Println("2. Ensure mock_data.csv exists")
		fmt.Println("3. Ensure existing_standalone/ directory exists")
		return
	}

	// Clean up previous results
	os.RemoveAll(config.OutputDir)
	os.MkdirAll(config.OutputDir, 0755)

	fmt.Println("✅ Prerequisites verified")
	fmt.Println()

	// Run simple comparison
	results := runSimpleComparison(config)
	displaySimpleResults(results)

	fmt.Println("\n✅ Simple demo completed!")
	fmt.Println("\nFor comprehensive comparison, run:")
	fmt.Println("  go run demo_performance_comparison.go")
	fmt.Println("  OR")
	fmt.Println("  ./run_performance_demo.sh")
}

func verifySimplePrerequisites(config SimpleDemoConfig) error {
	// Check Go
	if _, err := exec.LookPath("go"); err != nil {
		return fmt.Errorf("Go is not installed")
	}

	// Check data file
	if _, err := os.Stat(config.DataFile); os.IsNotExist(err) {
		return fmt.Errorf("data file %s not found", config.DataFile)
	}

	// Check existing_standalone
	if _, err := os.Stat("existing_standalone"); os.IsNotExist(err) {
		return fmt.Errorf("existing_standalone directory not found")
	}

	return nil
}

type SimpleResult struct {
	Implementation string
	GenerationTime time.Duration
	StorageSize    int64
	QueryTime      time.Duration
	Success        bool
	Error          string
}

func runSimpleComparison(config SimpleDemoConfig) []SimpleResult {
	var results []SimpleResult

	fmt.Println("🔄 Running simple performance comparison...")
	fmt.Println()

	// Test Existing implementation
	fmt.Println("1️⃣  Testing Existing implementation...")
	existingResult := testSimpleExisting(config)
	results = append(results, existingResult)

	// Test UltraFast V1 implementation
	fmt.Println("2️⃣  Testing UltraFast V1 implementation...")
	ultrafastResult := testSimpleUltraFast(config)
	results = append(results, ultrafastResult)

	return results
}

func testSimpleExisting(config SimpleDemoConfig) SimpleResult {
	result := SimpleResult{Implementation: "Existing"}

	existingDir := filepath.Join(config.OutputDir, "existing")
	os.MkdirAll(existingDir, 0755)

	// Generate index
	fmt.Printf("  Generating indexes...")
	start := time.Now()
	cmd := exec.Command("go", "run", "./existing_standalone", "generate", config.DataFile, existingDir)
	if err := cmd.Run(); err != nil {
		result.Error = fmt.Sprintf("generation failed: %v", err)
		fmt.Printf(" ❌ FAILED\n")
		return result
	}
	result.GenerationTime = time.Since(start)
	fmt.Printf(" ✅ %v\n", result.GenerationTime)

	// Calculate storage size
	size, _ := calculateDirectorySize(existingDir)
	result.StorageSize = size

	// Test query
	fmt.Printf("  Testing query...")
	start = time.Now()
	cmd = exec.Command("go", "run", "./existing_standalone", "query", config.TestColumn, config.TestValue)
	if err := cmd.Run(); err != nil {
		result.Error = fmt.Sprintf("query failed: %v", err)
		fmt.Printf(" ❌ FAILED\n")
		return result
	}
	result.QueryTime = time.Since(start)
	result.Success = true
	fmt.Printf(" ✅ %v\n", result.QueryTime)

	return result
}

func testSimpleUltraFast(config SimpleDemoConfig) SimpleResult {
	result := SimpleResult{Implementation: "UltraFast V1"}

	ultrafastDir := filepath.Join(config.OutputDir, "ultrafast")
	os.MkdirAll(ultrafastDir, 0755)

	// Generate index
	fmt.Printf("  Generating indexes...")
	start := time.Now()
	cmd := exec.Command("go", "run", ".", "generate-full", config.DataFile, ultrafastDir, "demo_table")
	if err := cmd.Run(); err != nil {
		result.Error = fmt.Sprintf("generation failed: %v", err)
		fmt.Printf(" ❌ FAILED\n")
		return result
	}
	result.GenerationTime = time.Since(start)
	fmt.Printf(" ✅ %v\n", result.GenerationTime)

	// Calculate storage size
	size, _ := calculateDirectorySize(ultrafastDir)
	result.StorageSize = size

	// Test query
	fmt.Printf("  Testing query...")
	start = time.Now()
	filterExpr := fmt.Sprintf("%s=%s", config.TestColumn, config.TestValue)
	cmd = exec.Command("go", "run", ".", "query", ultrafastDir, "demo_table", filterExpr, config.TestColumn)
	if err := cmd.Run(); err != nil {
		result.Error = fmt.Sprintf("query failed: %v", err)
		fmt.Printf(" ❌ FAILED\n")
		return result
	}
	result.QueryTime = time.Since(start)
	result.Success = true
	fmt.Printf(" ✅ %v\n", result.QueryTime)

	return result
}

func calculateDirectorySize(dirPath string) (int64, error) {
	var size int64
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})
	return size, err
}

func displaySimpleResults(results []SimpleResult) {
	fmt.Println("\n📊 Simple Performance Comparison Results")
	fmt.Println("========================================")

	fmt.Printf("\nTest Query: %s = %s\n", "protocol", "TCP")
	fmt.Println()

	// Display results table
	fmt.Printf("%-15s %-15s %-12s %-12s %-8s\n", "Implementation", "Generation", "Storage", "Query", "Status")
	fmt.Println(strings.Repeat("-", 70))

	for _, result := range results {
		status := "✅ OK"
		if !result.Success {
			status = "❌ FAIL"
		}

		fmt.Printf("%-15s %-15v %-12s %-12v %-8s\n",
			result.Implementation,
			result.GenerationTime,
			formatBytes(result.StorageSize),
			result.QueryTime,
			status)

		if result.Error != "" {
			fmt.Printf("  Error: %s\n", result.Error)
		}
	}

	// Calculate improvements if both succeeded
	if len(results) >= 2 && results[0].Success && results[1].Success {
		fmt.Println()
		fmt.Println("📈 Performance Improvements:")

		// Query speed improvement
		existingTime := results[0].QueryTime.Seconds()
		ultrafastTime := results[1].QueryTime.Seconds()
		speedup := existingTime / ultrafastTime

		fmt.Printf("  Query Speed: %.1fx faster\n", speedup)

		// Storage efficiency
		existingSize := float64(results[0].StorageSize)
		ultrafastSize := float64(results[1].StorageSize)
		storageReduction := (1.0 - ultrafastSize/existingSize) * 100

		fmt.Printf("  Storage Size: %.1f%% smaller\n", storageReduction)

		// Generation time comparison
		existingGen := results[0].GenerationTime.Seconds()
		ultrafastGen := results[1].GenerationTime.Seconds()
		genSpeedup := existingGen / ultrafastGen

		fmt.Printf("  Generation: %.1fx faster\n", genSpeedup)
	}

	fmt.Println()
	fmt.Printf("📁 Results saved to: %s/\n", "simple_demo_results")
}

func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
