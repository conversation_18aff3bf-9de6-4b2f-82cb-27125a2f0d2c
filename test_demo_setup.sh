#!/bin/bash

# Test script to verify demo setup and prerequisites
echo "🧪 Testing UltraFast Index Demo Setup"
echo "====================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -n "Testing $test_name... "
    
    if eval "$test_command" &>/dev/null; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Test Go installation
run_test "Go installation" "go version"

# Test required files
run_test "mock_data.csv exists" "[ -f mock_data.csv ]"
run_test "existing_standalone directory" "[ -d existing_standalone ]"
run_test "existing_standalone/main.go" "[ -f existing_standalone/main.go ]"
run_test "existing_standalone/indexing.go" "[ -f existing_standalone/indexing.go ]"

# Test main UltraFast files
run_test "main.go exists" "[ -f main.go ]"
run_test "ultrafast.go exists" "[ -f ultrafast.go ]"
run_test "ultrafast_v2.go exists" "[ -f ultrafast_v2.go ]"
run_test "demo_performance_comparison.go" "[ -f demo_performance_comparison.go ]"

# Test Go module compilation
echo ""
echo "🔧 Testing compilation..."

run_test "existing_standalone compiles" "cd existing_standalone && go build -o /tmp/existing_test . && rm -f /tmp/existing_test"
run_test "main UltraFast compiles" "go build -o /tmp/ultrafast_test . && rm -f /tmp/ultrafast_test"
run_test "demo compiles" "go build -o /tmp/demo_test demo_performance_comparison.go && rm -f /tmp/demo_test"

# Test basic functionality
echo ""
echo "🚀 Testing basic functionality..."

# Test existing_standalone help
run_test "existing_standalone help" "cd existing_standalone && timeout 5s go run . help"

# Test main program help
run_test "UltraFast help" "timeout 5s go run . help"

# Test CSV file format
echo ""
echo "📊 Testing data file..."

if [ -f "mock_data.csv" ]; then
    LINES=$(wc -l < mock_data.csv)
    COLUMNS=$(head -1 mock_data.csv | tr ',' '\n' | wc -l)
    
    echo "  📈 Dataset: $LINES lines, $COLUMNS columns"
    
    if [ "$LINES" -gt 1000 ]; then
        echo -e "  ${GREEN}✅ Dataset size adequate for testing${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "  ${YELLOW}⚠️  Dataset might be small for meaningful results${NC}"
    fi
    
    if [ "$COLUMNS" -gt 10 ]; then
        echo -e "  ${GREEN}✅ Column count good for testing${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "  ${YELLOW}⚠️  Limited columns for comprehensive testing${NC}"
    fi
else
    echo -e "  ${RED}❌ mock_data.csv not found${NC}"
    ((TESTS_FAILED++))
fi

# Test directory permissions
echo ""
echo "📁 Testing permissions..."

run_test "can create demo_results directory" "mkdir -p test_demo_dir && rmdir test_demo_dir"
run_test "can write to current directory" "touch test_file && rm test_file"

# Summary
echo ""
echo "📋 Test Summary"
echo "==============="
echo -e "Tests passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests failed: ${RED}$TESTS_FAILED${NC}"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! Demo is ready to run.${NC}"
    echo ""
    echo "To run the demo:"
    echo "  ./run_performance_demo.sh"
    echo "  OR"
    echo "  go run demo_performance_comparison.go"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please fix the issues before running the demo.${NC}"
    echo ""
    echo "Common fixes:"
    echo "  - Install Go 1.19+ from https://golang.org/dl/"
    echo "  - Ensure mock_data.csv is present"
    echo "  - Run 'go mod tidy' in both directories"
    echo "  - Check file permissions"
    exit 1
fi
