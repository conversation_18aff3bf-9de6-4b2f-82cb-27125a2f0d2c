package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// DemoConfig holds configuration for the performance demo
type DemoConfig struct {
	DataFile        string
	ExistingDir     string
	ExistingInxDir  string
	UltraFastV1Dir  string
	UltraFastV2Dir  string
	OutputDir       string
	WarmupRuns      int
	BenchmarkRuns   int
	TestQueries     []TestQuery
}

// TestQuery represents a query to test
type TestQuery struct {
	Column      string
	Value       string
	Description string
}

// PerformanceResult holds results for one implementation
type PerformanceResult struct {
	Implementation   string
	GenerationTime   time.Duration
	StorageSize      int64
	QueryTimes       []time.Duration
	AverageQueryTime time.Duration
	QueriesPerSecond float64
	Success          bool
	Error            string
}

// DemoResults holds all performance results
type DemoResults struct {
	Existing     PerformanceResult
	ExistingInx  PerformanceResult
	UltraFastV1  PerformanceResult
	UltraFastV2  PerformanceResult
	DatasetSize  int
	TestQueries  []TestQuery
}

func main() {
	fmt.Println("🚀 UltraFast Index Comprehensive Performance Demo")
	fmt.Println("=================================================")
	fmt.Println()

	config := DemoConfig{
		DataFile:       "mock_data.csv",
		ExistingDir:    "demo_results/existing",
		ExistingInxDir: "demo_results/existing_inx",
		UltraFastV1Dir: "demo_results/ultrafast_v1",
		UltraFastV2Dir: "demo_results/ultrafast_v2",
		OutputDir:      "demo_results",
		WarmupRuns:     2,
		BenchmarkRuns:  5,
		TestQueries: []TestQuery{
			{"protocol", "TCP", "High selectivity query"},
			{"source_username", "odjordjevicrp", "Medium selectivity query"},
			{"rule_name", "ALLOW_HTTP", "Low selectivity query"},
			{"destination_country", "United States", "Geographic query"},
			{"action", "ALLOW", "Action-based query"},
		},
	}

	// Verify prerequisites
	if err := verifyPrerequisites(config); err != nil {
		fmt.Printf("❌ Prerequisites check failed: %v\n", err)
		return
	}

	// Clean up and create output directories
	os.RemoveAll(config.OutputDir)
	for _, dir := range []string{config.ExistingDir, config.ExistingInxDir, config.UltraFastV1Dir, config.UltraFastV2Dir} {
		os.MkdirAll(dir, 0755)
	}

	fmt.Println("✅ Prerequisites verified")
	fmt.Printf("📊 Dataset: %s\n", config.DataFile)
	fmt.Printf("🔍 Test queries: %d\n", len(config.TestQueries))
	fmt.Println()

	// Run comprehensive comparison
	results := runComprehensiveComparison(config)
	displayComprehensiveResults(results)

	fmt.Println("\n✅ Comprehensive demo completed!")
	fmt.Printf("📁 Results saved to: %s/\n", config.OutputDir)
}

func verifyPrerequisites(config DemoConfig) error {
	// Check Go
	if _, err := exec.LookPath("go"); err != nil {
		return fmt.Errorf("Go is not installed")
	}

	// Check data file
	if _, err := os.Stat(config.DataFile); os.IsNotExist(err) {
		return fmt.Errorf("data file %s not found", config.DataFile)
	}

	// Check existing_standalone
	if _, err := os.Stat("existing_standalone"); os.IsNotExist(err) {
		return fmt.Errorf("existing_standalone directory not found")
	}

	return nil
}

func runComprehensiveComparison(config DemoConfig) DemoResults {
	results := DemoResults{
		TestQueries: config.TestQueries,
		DatasetSize: 33023, // Known from mock_data.csv
	}

	fmt.Println("🔄 Running comprehensive performance comparison...")
	fmt.Println()

	// Test all implementations
	fmt.Println("1️⃣  Testing Existing implementation...")
	results.Existing = testExistingImplementation(config)

	fmt.Println("2️⃣  Testing UltraFast V1 implementation...")
	results.UltraFastV1 = testUltraFastV1Implementation(config)

	return results
}

func testExistingImplementation(config DemoConfig) PerformanceResult {
	result := PerformanceResult{Implementation: "Existing"}

	// Generate index
	fmt.Printf("  Generating indexes...")
	start := time.Now()
	cmd := exec.Command("bash", "-c", "cd existing_standalone && go run . generate ../"+config.DataFile+" ../"+config.ExistingDir)
	if err := cmd.Run(); err != nil {
		result.Error = fmt.Sprintf("generation failed: %v", err)
		fmt.Printf(" ❌ FAILED\n")
		return result
	}
	result.GenerationTime = time.Since(start)
	fmt.Printf(" ✅ %v\n", result.GenerationTime)

	// Calculate storage size
	size, _ := calculateDirectorySize(config.ExistingDir)
	result.StorageSize = size

	// Test queries
	fmt.Printf("  Testing queries...")
	var allTimes []time.Duration
	for _, query := range config.TestQueries {
		var queryTimes []time.Duration

		// Benchmark runs
		for i := 0; i < config.BenchmarkRuns; i++ {
			start := time.Now()
			cmd := exec.Command("bash", "-c", "cd existing_standalone && go run . query ../"+config.ExistingDir+" "+query.Column+" "+query.Value)
			if err := cmd.Run(); err != nil {
				result.Error = fmt.Sprintf("query failed: %v", err)
				fmt.Printf(" ❌ FAILED\n")
				return result
			}
			queryTimes = append(queryTimes, time.Since(start))
		}

		avgTime := calculateAverage(queryTimes)
		allTimes = append(allTimes, avgTime)
	}

	result.QueryTimes = allTimes
	result.AverageQueryTime = calculateAverage(allTimes)
	result.QueriesPerSecond = 1.0 / result.AverageQueryTime.Seconds()
	result.Success = true
	fmt.Printf(" ✅ %v avg\n", result.AverageQueryTime)

	return result
}

func testUltraFastV1Implementation(config DemoConfig) PerformanceResult {
	result := PerformanceResult{Implementation: "UltraFast V1"}

	// Generate index
	fmt.Printf("  Generating indexes...")
	start := time.Now()
	cmd := exec.Command("go", "run", "main.go", "ultrafast.go", "ultrafast_v2.go", "queryparser.go", "rowstore.go", "generate-full", config.DataFile, config.UltraFastV1Dir, "demo_table")
	if err := cmd.Run(); err != nil {
		result.Error = fmt.Sprintf("generation failed: %v", err)
		fmt.Printf(" ❌ FAILED\n")
		return result
	}
	result.GenerationTime = time.Since(start)
	fmt.Printf(" ✅ %v\n", result.GenerationTime)

	// Calculate storage size
	size, _ := calculateDirectorySize(config.UltraFastV1Dir)
	result.StorageSize = size

	// Test queries
	fmt.Printf("  Testing queries...")
	var allTimes []time.Duration
	for _, query := range config.TestQueries {
		var queryTimes []time.Duration

		// Benchmark runs
		for i := 0; i < config.BenchmarkRuns; i++ {
			start := time.Now()
			filterExpr := fmt.Sprintf("%s=%s", query.Column, query.Value)
			cmd := exec.Command("go", "run", "main.go", "ultrafast.go", "ultrafast_v2.go", "queryparser.go", "rowstore.go", "query", config.UltraFastV1Dir, "demo_table", filterExpr, query.Column)
			if err := cmd.Run(); err != nil {
				result.Error = fmt.Sprintf("query failed: %v", err)
				fmt.Printf(" ❌ FAILED\n")
				return result
			}
			queryTimes = append(queryTimes, time.Since(start))
		}

		avgTime := calculateAverage(queryTimes)
		allTimes = append(allTimes, avgTime)
	}

	result.QueryTimes = allTimes
	result.AverageQueryTime = calculateAverage(allTimes)
	result.QueriesPerSecond = 1.0 / result.AverageQueryTime.Seconds()
	result.Success = true
	fmt.Printf(" ✅ %v avg\n", result.AverageQueryTime)

	return result
}

func calculateDirectorySize(dirPath string) (int64, error) {
	var size int64
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})
	return size, err
}

func calculateAverage(times []time.Duration) time.Duration {
	if len(times) == 0 {
		return 0
	}

	var total time.Duration
	for _, t := range times {
		total += t
	}

	return total / time.Duration(len(times))
}

func displayComprehensiveResults(results DemoResults) {
	fmt.Println("\n📈 Comprehensive Performance Comparison Results")
	fmt.Println("===============================================")

	// Dataset information
	fmt.Printf("\n📊 Dataset: %d records\n", results.DatasetSize)
	fmt.Printf("🔍 Test queries: %d\n", len(results.TestQueries))

	// Index generation comparison
	fmt.Println("\n⏱️  Index Generation Time:")
	if results.Existing.Success {
		fmt.Printf("  Existing:     %v\n", results.Existing.GenerationTime)
	}
	if results.UltraFastV1.Success {
		fmt.Printf("  UltraFast V1: %v\n", results.UltraFastV1.GenerationTime)
	}

	// Storage size comparison
	fmt.Println("\n💾 Storage Size:")
	if results.Existing.Success {
		fmt.Printf("  Existing:     %s\n", formatBytes(results.Existing.StorageSize))
	}
	if results.UltraFastV1.Success {
		fmt.Printf("  UltraFast V1: %s\n", formatBytes(results.UltraFastV1.StorageSize))
	}

	// Query performance comparison
	fmt.Println("\n🚀 Average Query Performance:")
	if results.Existing.Success {
		fmt.Printf("  Existing:     %v (%0.1f QPS)\n", results.Existing.AverageQueryTime, results.Existing.QueriesPerSecond)
	}
	if results.UltraFastV1.Success {
		fmt.Printf("  UltraFast V1: %v (%0.1f QPS)\n", results.UltraFastV1.AverageQueryTime, results.UltraFastV1.QueriesPerSecond)
	}

	// Performance improvements
	if results.Existing.Success && results.UltraFastV1.Success {
		fmt.Println("\n📊 Performance Improvements (vs Existing):")
		
		// Query speed improvements
		existingTime := results.Existing.AverageQueryTime.Seconds()
		fmt.Printf("  UltraFast V1 Query Speed: %0.1fx faster\n", existingTime/results.UltraFastV1.AverageQueryTime.Seconds())

		// Storage efficiency improvements
		existingSize := float64(results.Existing.StorageSize)
		fmt.Printf("  UltraFast V1 Storage:     %0.1f%% of original size\n", float64(results.UltraFastV1.StorageSize)/existingSize*100)
	}
}

func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
